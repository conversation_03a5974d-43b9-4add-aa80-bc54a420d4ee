<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Chat</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>

<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <button id="sidebarToggle" class="sidebar-toggle">☰</button>
                <h1>Ollama Chat</h1>
            </div>
            <div class="header-controls">
                <div id="contextStatus" class="context-status" style="display: none;">
                    <div class="context-bar">
                        <div id="contextProgress" class="context-progress"></div>
                    </div>
                    <span id="contextText" class="context-text">0% used</span>
                </div>
                <select id="modelSelect" class="model-select">
                    <option value="">Select Model...</option>
                </select>
                <button id="mcpBtn" class="mcp-btn">MCP</button>
            </div>
        </header>

        <!-- Sidebar Backdrop -->
        <div id="sidebarBackdrop" class="sidebar-backdrop"></div>

        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h3>Chat Sessions</h3>
            </div>
            <div class="sidebar-content">
                <button id="newChatBtn" class="new-chat-btn">
                    <span>+</span> New Chat
                </button>
                <div class="chat-sessions-list">
                    <h4>Recent Sessions</h4>
                    <div id="chatSessions" class="chat-sessions"></div>
                </div>
            </div>
        </aside>

        <!-- Main Chat Area -->
        <main class="chat-container">
            <div id="contextWarning" class="context-warning" style="display: none;">
                <div class="warning-content">
                    <span class="warning-icon">⚠️</span>
                    <span class="warning-text">Context window is nearly full. Consider starting a new session for
                        better responses.</span>
                    <button id="startNewSessionBtn" class="start-new-session-btn">Start New Session</button>
                    <button id="dismissWarningBtn" class="dismiss-warning-btn">×</button>
                </div>
            </div>

            <div id="messages" class="messages"></div>

            <div class="input-area">
                <div class="input-container">
                    <div class="input-row">
                        <textarea id="messageInput" placeholder="Type your message..." rows="1"></textarea>
                        <div class="input-buttons">
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                            <button id="attachBtn" class="attach-btn" title="Attach Image">📎</button>
                            <button id="sendBtn" class="send-btn">Send</button>
                        </div>
                    </div>
                    <div id="imagePreview" class="image-preview" style="display: none;"></div>
                </div>
            </div>
        </main>
    </div>

    <!-- MCP Modal -->
    <div id="mcpModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>MCP Servers</h2>
                <button id="closeMcpModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 1. API Key Section First -->
                <div class="api-key-section">
                    <h3>🔑 Smithery Authentication</h3>
                    <div class="api-key-form">
                        <input type="password" id="smitheryApiKey" placeholder="Enter your Smithery API key"
                            class="server-input">
                        <button id="saveApiKeyBtn" class="add-server-btn">Save API Key</button>
                    </div>
                    <div class="api-key-help">
                        <p><strong>Get your API key:</strong></p>
                        <ol>
                            <li>Visit <a href="https://smithery.ai/account/api-keys"
                                    target="_blank">smithery.ai/account/api-keys</a></li>
                            <li>Create a new API key</li>
                            <li>Copy and paste it above</li>
                        </ol>
                        <p><small>🔒 Your API key is stored locally and used for MCP server authentication.</small></p>
                    </div>
                </div>

                <!-- 2. Enabled Servers List -->
                <div class="enabled-servers-section">
                    <h3>🟢 Enabled MCP Servers</h3>
                    <div id="mcpServers" class="mcp-servers"></div>
                </div>

                <!-- 3. Add by URL Section -->
                <div class="add-server-section">
                    <h3>🔗 Add Server by URL</h3>
                    <div class="add-server-form">
                        <input type="text" id="serverUrl" placeholder="Enter Smithery.ai MCP server URL"
                            class="server-input">
                        <input type="text" id="serverName" placeholder="Server name (optional)" class="server-input">
                        <button id="addServerBtn" class="add-server-btn">Add Server</button>
                    </div>
                    <div class="server-help">
                        <p><strong>How to add MCP servers:</strong></p>
                        <ol>
                            <li>Visit <a href="https://smithery.ai" target="_blank">smithery.ai</a></li>
                            <li>Find the MCP server you want to use</li>
                            <li>Copy the server URL (e.g., @nickclyde/duckduckgo-mcp-server)</li>
                            <li>Paste it above and click "Add Server"</li>
                        </ol>
                    </div>
                </div>


            </div>
        </div>
    </div>



    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>

</html>