{"_nickclyde_duckduckgo-mcp-server": {"url": "https://server.smithery.ai/@nickclyde/duckduckgo-mcp-server/mcp?api_key=79e77008-74a4-4a04-8938-20db7d900ec2&profile=dynamic-server", "description": "Enable web search capabilities through DuckDuckGo", "tools": ["search", "fetch_content"], "connected": true, "custom": true, "tool_schemas": [{"name": "search", "description": "\n    Search DuckDuckGo and return formatted results.\n\n    Args:\n        query: The search query string\n        max_results: Maximum number of results to return (default: 10)\n        ctx: MCP context for logging\n    ", "schema": {"type": "object", "title": "searchArguments", "required": ["query"], "properties": {"query": {"type": "string", "title": "Query"}, "max_results": {"type": "integer", "title": "Max Results", "default": 10}}}}, {"name": "fetch_content", "description": "\n    Fetch and parse content from a webpage URL.\n\n    Args:\n        url: The webpage URL to fetch content from\n        ctx: MCP context for logging\n    ", "schema": {"type": "object", "title": "fetch_contentArguments", "required": ["url"], "properties": {"url": {"type": "string", "title": "Url"}}}}]}, "_upstash_context7-mcp": {"url": "https://server.smithery.ai/@upstash/context7-mcp/mcp?api_key=84ea9772-34a0-480d-a893-978e879eac95&profile=courageous-guppy-HiObl6", "description": "Custom MCP server: Context7 Mcp", "tools": ["resolve-library-id", "get-library-docs"], "connected": true, "custom": true, "tool_schemas": [{"name": "resolve-library-id", "description": "Resolves a package/product name to a Context7-compatible library ID and returns a list of matching libraries.\n\nYou MUST call this function before 'get-library-docs' to obtain a valid Context7-compatible library ID UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.\n\nSelection Process:\n1. Analyze the query to understand what library/package the user is looking for\n2. Return the most relevant match based on:\n- Name similarity to the query (exact matches prioritized)\n- Description relevance to the query's intent\n- Documentation coverage (prioritize libraries with higher Code Snippet counts)\n- Trust score (consider libraries with scores of 7-10 more authoritative)\n\nResponse Format:\n- Return the selected library ID in a clearly marked section\n- Provide a brief explanation for why this library was chosen\n- If multiple good matches exist, acknowledge this but proceed with the most relevant one\n- If no good matches exist, clearly state this and suggest query refinements\n\nFor ambiguous queries, request clarification before proceeding with a best-guess match.", "schema": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["libraryName"], "properties": {"libraryName": {"type": "string", "description": "Library name to search for and retrieve a Context7-compatible library ID."}}, "additionalProperties": false}}, {"name": "get-library-docs", "description": "Fetches up-to-date documentation for a library. You must call 'resolve-library-id' first to obtain the exact Context7-compatible library ID required to use this tool, UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.", "schema": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["context7CompatibleLibraryID"], "properties": {"topic": {"type": "string", "description": "Topic to focus documentation on (e.g., 'hooks', 'routing')."}, "tokens": {"type": "number", "description": "Maximum number of tokens of documentation to retrieve (default: 10000). Higher values provide more context but consume more tokens."}, "context7CompatibleLibraryID": {"type": "string", "description": "Exact Context7-compatible library ID (e.g., '/mongodb/docs', '/vercel/next.js', '/supabase/supabase', '/vercel/next.js/v14.3.0-canary.87') retrieved from 'resolve-library-id' or directly from user query in the format '/org/project' or '/org/project/version'."}}, "additionalProperties": false}}]}}