{"1b65c14f-9280-43db-bca6-86ffea8a05e2": {"title": "Chat 1b65c14f", "created_at": "2025-07-17T04:12:36.881891", "messages": [{"role": "user", "content": "hi", "timestamp": "2025-07-17T04:12:36.883173", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 1}, {"role": "user", "content": "hello", "timestamp": "2025-07-17T04:13:47.128547", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 2}, {"role": "assistant", "content": "I remember that your name is <PERSON> and you work as a software engineer. How can I assist you today with your software engineering work? Are you looking for information on a specific programming language, framework, tool, or perhaps help with a coding problem?", "content_html": "<p>I remember that your name is <PERSON> and you work as a software engineer. How can I assist you today with your software engineering work? Are you looking for information on a specific programming language, framework, tool, or perhaps help with a coding problem?</p>", "timestamp": "2025-07-17T04:13:58.101747", "model": "mistral-small3.2:latest", "tools_used": [], "tokens": 52, "context_status": {"current_tokens": 52, "context_window": 8192, "usage_percentage": 0.634765625, "tokens_remaining": 8140, "should_start_new_session": false}}]}, "memory-test-session": {"title": "Chat memory-t", "created_at": "2025-07-17T04:12:47.563430", "messages": [{"role": "user", "content": "My name is <PERSON> and I work as a software engineer", "timestamp": "2025-07-17T04:12:47.565082", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 11}, {"role": "assistant", "content": "Hello <PERSON>! Nice to meet you. How can I assist you today with your software engineering work? Are you looking for information on a specific programming language, framework, tool, or perhaps help with a coding problem?", "content_html": "<p>Hello <PERSON>! Nice to meet you. How can I assist you today with your software engineering work? Are you looking for information on a specific programming language, framework, tool, or perhaps help with a coding problem?</p>", "timestamp": "2025-07-17T04:12:56.214896", "model": "mistral-small3.2:latest", "tools_used": [], "tokens": 54, "context_status": {"current_tokens": 54, "context_window": 8192, "usage_percentage": 0.6591796875, "tokens_remaining": 8138, "should_start_new_session": false}}, {"role": "user", "content": "What do you remember about me?", "timestamp": "2025-07-17T04:13:46.263411", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 61}]}, "multi-model-test": {"title": "Chat multi-mo", "created_at": "2025-07-17T04:20:51.726279", "messages": [{"role": "user", "content": "Test with mistral model", "timestamp": "2025-07-17T04:20:51.727099", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 5}, {"role": "assistant", "content": "I'm sorry, but I don't have access to the tools needed to test with the Mistral model. However, I'm here to help answer any other questions you might have!", "content_html": "<p>I'm sorry, but I don't have access to the tools needed to test with the Mistral model. However, I'm here to help answer any other questions you might have!</p>", "timestamp": "2025-07-17T04:21:35.964524", "model": "mistral-small3.2:latest", "tools_used": [], "tokens": 42, "context_status": {"current_tokens": 42, "context_window": 8192, "usage_percentage": 0.5126953125, "tokens_remaining": 8150, "should_start_new_session": false, "model_name": "mistral-small3.2:latest", "model_contexts": {"mistral-small3.2:latest": {"tokens": 42, "context_window": 8192}}}}]}}