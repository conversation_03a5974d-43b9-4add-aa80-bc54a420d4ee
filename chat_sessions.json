{"2a2c24a9-43a7-4ded-a037-ac272a6d7667": {"title": "Chat 2a2c24a9", "created_at": "2025-07-17T04:26:11.895178", "messages": [{"role": "user", "content": "what tools you have avalaibe", "timestamp": "2025-07-17T04:26:15.982800", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 7}, {"role": "user", "content": "what tools you have avalaibe", "timestamp": "2025-07-17T04:27:35.719422", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 14}]}, "error-test-session": {"title": "Chat error-te", "created_at": "2025-07-17T04:28:27.503787", "messages": [{"role": "user", "content": "Hello, test message", "timestamp": "2025-07-17T04:28:27.504731", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 4}, {"role": "assistant", "content": "Hello! How can I assist you today?", "content_html": "<p>Hello! How can I assist you today?</p>", "timestamp": "2025-07-17T04:28:31.460996", "model": "mistral-small3.2:latest", "tools_used": [], "tokens": 13, "context_status": {"current_tokens": 13, "context_window": 8192, "usage_percentage": 0.15869140625, "tokens_remaining": 8179, "should_start_new_session": false, "model_name": "mistral-small3.2:latest", "model_contexts": {"mistral-small3.2:latest": {"tokens": 13, "context_window": 8192}, "magistral:latest": {"tokens": 121, "context_window": 8192}}}}, {"role": "user", "content": "what tools you have avalaibe", "timestamp": "2025-07-17T04:29:12.656424", "has_image": false, "model": "magistral:latest", "tokens": 7}, {"role": "user", "content": "what tools you have avalaibe", "timestamp": "2025-07-17T04:30:34.463717", "has_image": false, "model": "magistral:latest", "tokens": 14}, {"role": "user", "content": "what tools you have avalaibe", "timestamp": "2025-07-17T04:30:41.042512", "has_image": false, "model": "magistral:latest", "tokens": 21}, {"role": "assistant", "content": "I have access to the following tools:\n\n1. **DuckDuckGo Search**: Search for information on the web and return formatted results.\n2. **Webpage Content Fetcher**: Fetch and parse content from a specific webpage URL.\n3. **Context7 Library ID Resolver**: Resolve package/product names to Context7-compatible library IDs.\n4. **Context7 Library Documentation Fetcher**: Fetch up-to-date documentation for libraries using their Context7-compatible library IDs.\n\nHow can I assist you today?", "content_html": "<p>I have access to the following tools:</p>\n<ol>\n<li><strong>DuckDuckGo Search</strong>: Search for information on the web and return formatted results.</li>\n<li><strong>Webpage Content Fetcher</strong>: Fetch and parse content from a specific webpage URL.</li>\n<li><strong>Context7 Library ID Resolver</strong>: Resolve package/product names to Context7-compatible library IDs.</li>\n<li><strong>Context7 Library Documentation Fetcher</strong>: Fetch up-to-date documentation for libraries using their Context7-compatible library IDs.</li>\n</ol>\n<p>How can I assist you today?</p>", "timestamp": "2025-07-17T04:31:46.820079", "model": "magistral:latest", "tools_used": [], "tokens": 121, "context_status": {"current_tokens": 121, "context_window": 8192, "usage_percentage": 1.47705078125, "tokens_remaining": 8071, "should_start_new_session": false, "model_name": "magistral:latest", "model_contexts": {"mistral-small3.2:latest": {"tokens": 13, "context_window": 8192}, "magistral:latest": {"tokens": 121, "context_window": 8192}}}}]}, "streaming-test-session": {"title": "Chat streamin", "created_at": "2025-07-17T04:28:43.276840", "messages": [{"role": "user", "content": "Tell me a short story", "timestamp": "2025-07-17T04:28:43.277676", "has_image": false, "model": "mistral-small3.2:latest", "tokens": 5}]}}