echo "🤖 Starting Ollama Chat Application"
echo "=================================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip &> /dev/null; then
    echo "❌ pip is not installed. Please install pip first."
    exit 1
fi

# Install dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Check if Ollama is running
echo "🔍 Checking Ollama connection..."
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "✅ Ollama is running"
else
    echo "⚠️  Ollama is not running. Please start it with: ollama serve"
    echo "   You can continue anyway, but the app won't work until Ollama is started."
fi

echo "🚀 Starting Flask server..."
echo "🌐 Open your browser to: http://localhost:5000"
echo "⌨️  Press Ctrl+C to stop the server"
echo "=================================="

python app.py
