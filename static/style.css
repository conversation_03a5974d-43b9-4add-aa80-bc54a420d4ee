* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    color: #333;
}

.sidebar-toggle:hover {
    background: #f0f0f0;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Main Content Layout */
.main-content {
    display: flex;
    height: calc(100vh - 80px);
    /* Adjust based on header height */
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar.hidden {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #333;
}

.new-chat-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
}

.new-chat-btn:hover {
    background: #0056b3;
}

.chat-sessions {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.chat-session {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-session:hover {
    background: #f0f0f0;
    border-color: #007bff;
}

.chat-session.active {
    background: #e3f2fd;
    border-color: #007bff;
}

.chat-session-info {
    flex: 1;
    min-width: 0;
}

.chat-session-title {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-session-preview {
    font-size: 0.85rem;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-session-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-session:hover .chat-session-actions {
    opacity: 1;
}

.delete-session-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.75rem;
}

.delete-session-btn:hover {
    background: #c82333;
}

.model-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    min-width: 200px;
}

.mcp-btn {
    padding: 0.5rem 1rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.mcp-btn:hover {
    background: #0056b3;
}



/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: none;
}

.message {
    padding: 1rem;
    border-radius: 8px;
    max-width: 80%;
}

.message.user {
    background: #007bff;
    color: white;
    align-self: flex-end;
}

.message.assistant {
    background: white;
    border: 1px solid #e0e0e0;
    align-self: flex-start;
}

.message.error {
    background: #dc3545;
    color: white;
    align-self: center;
}

.message.streaming {
    background: #f0f0f0;
    color: #666;
    align-self: flex-start;
}

.typing-indicator {
    animation: typing 1.5s infinite;
    font-size: 1.2rem;
}

@keyframes typing {

    0%,
    20% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

.message.info {
    background: #17a2b8;
    color: white;
    align-self: center;
    font-size: 0.9rem;
}

/* Input Area */
.input-area {
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 1rem;
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
}

.input-row {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.input-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    font-family: inherit;
}



.attach-btn {
    padding: 0.75rem;
    background: #ffc107;
    color: #212529;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    height: 44px;
    min-width: 44px;
    font-size: 1.2rem;
}

.attach-btn:hover {
    background: #e0a800;
}

.attach-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.image-preview {
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

.image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 4px;
}

.image-preview .remove-image {
    margin-left: 1rem;
    padding: 0.25rem 0.5rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.send-btn {
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    height: 44px;
}

.send-btn:hover {
    background: #0056b3;
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

/* Add Server Section */
.add-server-section,
.api-key-section,
.registry-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.add-server-section h3,
.api-key-section h3,
.registry-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: #333;
}

.add-server-form,
.api-key-form {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.server-input {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    font-family: inherit;
}

.server-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.add-server-btn {
    padding: 0.75rem 1rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.add-server-btn:hover {
    background: #0056b3;
}

.add-server-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.server-help,
.api-key-help {
    margin-top: 1rem;
    padding: 1rem;
    background: #e3f2fd;
    border-radius: 4px;
    border-left: 4px solid #2196f3;
}

.server-help p,
.api-key-help p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #333;
    font-weight: 500;
}

.server-help ol,
.api-key-help ol {
    margin: 0.5rem 0 0 1rem;
    font-size: 0.85rem;
    color: #555;
}

.server-help a,
.api-key-help a {
    color: #007bff;
    text-decoration: none;
}

.server-help a:hover,
.api-key-help a:hover {
    text-decoration: underline;
}

/* Registry Section */
.registry-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.registry-results {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

.registry-loading {
    padding: 2rem;
    text-align: center;
    color: #666;
}

.registry-server {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.registry-server:hover {
    background-color: #f8f9fa;
}

.registry-server:last-child {
    border-bottom: none;
}

.registry-server-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.registry-server-name {
    font-weight: 600;
    color: #333;
    margin: 0;
}

.registry-server-author {
    font-size: 0.9rem;
    color: #666;
    margin-left: 0.5rem;
}

.registry-server-description {
    color: #555;
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.registry-server-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #777;
    margin: 0.5rem 0;
}

.registry-server-tools {
    font-size: 0.8rem;
    color: #666;
    margin: 0.5rem 0;
}

.registry-server-actions {
    margin-top: 0.5rem;
}

.add-registry-server-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.add-registry-server-btn:hover {
    background: #218838;
}

.registry-empty {
    padding: 2rem;
    text-align: center;
    color: #666;
}

/* MCP Servers */
.mcp-servers {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mcp-server {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
}

.mcp-server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.mcp-server-name {
    font-weight: 600;
    color: #333;
}

.mcp-server-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.mcp-server-status.connected {
    background: #d4edda;
    color: #155724;
}

.mcp-server-status.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.mcp-server-description {
    color: #666;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.mcp-server-tools {
    font-size: 0.875rem;
    color: #666;
}

.mcp-server-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

.connect-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.connect-btn.connect {
    background: #28a745;
    color: white;
}

.connect-btn.disconnect {
    background: #dc3545;
    color: white;
}

.connect-btn:hover {
    opacity: 0.9;
}

.connect-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.delete-btn {
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.85rem;
}

.delete-btn:hover {
    background: #c82333;
}

.delete-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Chat History */
.history-controls {
    margin-bottom: 1rem;
    text-align: right;
}

.clear-history-btn {
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.clear-history-btn:hover {
    background: #c82333;
}

.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0.5rem;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item.user {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
}

.history-item.assistant {
    background: #fff;
    border-left: 4px solid #28a745;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-role {
    font-weight: 600;
    text-transform: capitalize;
}

.history-timestamp {
    font-size: 0.8rem;
    color: #666;
}

.history-content {
    white-space: pre-wrap;
    line-height: 1.4;
}

.history-meta {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Loading */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .message {
        max-width: 95%;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}