/* Color Palette */
:root {
    --primary-100: #2E8B57;
    --primary-200: #61bc84;
    --primary-300: #c6ffe6;
    --accent-100: #8FBC8F;
    --accent-200: #345e37;
    --text-100: #FFFFFF;
    --text-200: #e0e0e0;
    --bg-100: #1E1E1E;
    --bg-200: #2d2d2d;
    --bg-300: #454545;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--bg-100) 0%, var(--bg-200) 50%, var(--bg-300) 100%);
    min-height: 100vh;
    color: var(--text-100);
    overflow-x: hidden;
}

/* Glassmorphism Base Classes */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.glass-primary {
    background: rgba(46, 139, 87, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(46, 139, 87, 0.3);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(46, 139, 87, 0.2);
}

.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 20px;
    gap: 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.sidebar-toggle {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 12px;
    color: var(--text-100);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-100);
    background: linear-gradient(135deg, var(--primary-200), var(--primary-300));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-controls {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

/* Context Status */
.context-status {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 13px;
    color: var(--text-200);
    background: rgba(255, 255, 255, 0.05);
    padding: 8px 16px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.context-bar {
    width: 80px;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.context-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-200) 0%, var(--accent-100) 70%, #F44336 90%);
    transition: width 0.3s ease;
    width: 0%;
    border-radius: 4px;
}

.context-text {
    font-weight: 500;
    min-width: 60px;
    color: var(--text-100);
}

/* Context Warning */
.context-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffc107;
    border-radius: 8px;
    margin: 10px;
    padding: 0;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.warning-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
}

.warning-icon {
    font-size: 18px;
}

.warning-text {
    flex: 1;
    color: #856404;
    font-weight: 500;
}

.start-new-session-btn {
    background: #ffc107;
    color: #212529;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.start-new-session-btn:hover {
    background: #e0a800;
}

.dismiss-warning-btn {
    background: none;
    border: none;
    color: #856404;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s;
}

.dismiss-warning-btn:hover {
    background: rgba(133, 100, 4, 0.1);
}

/* Main Content Layout */
.main-content {
    display: flex;
    height: calc(100vh - 140px);
    gap: 20px;
    transition: all 0.3s ease;
}

/* Adjust main content when sidebar is hidden */
.main-content.sidebar-hidden {
    margin-left: -300px;
    /* Sidebar width */
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidebar.hidden {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-100);
    font-weight: 600;
}

.new-chat-btn {
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--text-100);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(46, 139, 87, 0.3);
}

.new-chat-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 139, 87, 0.4);
}

.chat-sessions {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.chat-session {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.chat-session:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-200);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.chat-session.active {
    background: linear-gradient(135deg, rgba(46, 139, 87, 0.2), rgba(97, 188, 132, 0.2));
    border-color: var(--primary-200);
    box-shadow: 0 6px 20px rgba(46, 139, 87, 0.3);
}

.chat-session-info {
    flex: 1;
    min-width: 0;
}

.chat-session-title {
    font-weight: 600;
    color: var(--text-100);
    margin-bottom: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1rem;
}

.chat-session-preview {
    font-size: 0.9rem;
    color: var(--text-200);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-session-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-session:hover .chat-session-actions {
    opacity: 1;
}

.delete-session-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.75rem;
}

.delete-session-btn:hover {
    background: #c82333;
}

.model-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    min-width: 200px;
}

.mcp-btn {
    padding: 0.5rem 1rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.mcp-btn:hover {
    background: #0056b3;
}



/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.messages {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: none;
}

.message {
    padding: 1.5rem 2rem;
    border-radius: 20px;
    max-width: 85%;
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.message:hover .message-actions {
    opacity: 1;
}

/* Message Actions */
.message-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    justify-content: flex-end;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message-action-btn {
    background: #f1f3f4;
    border: 1px solid #dadce0;
    border-radius: 16px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 12px;
    color: #5f6368;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.message-action-btn:hover {
    background: #e8eaed;
    border-color: #c4c7c5;
    color: #202124;
}

.message.user .message-actions {
    justify-content: flex-start;
}

.message.assistant .message-actions {
    justify-content: flex-end;
}

/* Edit mode styling */
.message.editing .message-content {
    display: none;
}

.message-edit-form {
    display: none;
}

.message.editing .message-edit-form {
    display: block;
}

.message-edit-textarea {
    width: 100%;
    min-height: 60px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: inherit;
    resize: vertical;
}

.message-edit-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.message-edit-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.message-edit-btn.save {
    background: #28a745;
    color: white;
}

.message-edit-btn.save:hover {
    background: #218838;
}

.message-edit-btn.cancel {
    background: #6c757d;
    color: white;
}

.message-edit-btn.cancel:hover {
    background: #5a6268;
}

.message.user {
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--text-100);
    align-self: flex-end;
    border: 1px solid rgba(46, 139, 87, 0.3);
}

.message.assistant {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    align-self: flex-start;
    color: var(--text-100);
}

.message.error {
    background: #dc3545;
    color: white;
    align-self: center;
}

.message.streaming {
    background: #f0f0f0;
    color: #666;
    align-self: flex-start;
}

.message.thinking {
    background: linear-gradient(135deg, rgba(198, 255, 230, 0.2), rgba(143, 188, 143, 0.2));
    border: 1px solid rgba(198, 255, 230, 0.3);
    color: var(--primary-300);
    align-self: flex-start;
    margin-bottom: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.thinking-header {
    font-weight: 600;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(198, 255, 230, 0.3);
    margin-bottom: 12px;
    user-select: none;
    color: var(--text-100);
    display: flex;
    align-items: center;
    gap: 8px;
}

.thinking-content {
    white-space: pre-wrap;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-200);
}

.typing-indicator {
    animation: typing 1.5s infinite;
    font-size: 1.2rem;
}

@keyframes typing {

    0%,
    20% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

.message.info {
    background: #17a2b8;
    color: white;
    align-self: center;
    font-size: 0.9rem;
}

/* Markdown Styling for Messages */
.message h1,
.message h2,
.message h3,
.message h4,
.message h5,
.message h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.25;
}

.message h1 {
    font-size: 1.5em;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.message h2 {
    font-size: 1.3em;
    border-bottom: 1px solid #eee;
    padding-bottom: 6px;
}

.message h3 {
    font-size: 1.2em;
}

.message h4 {
    font-size: 1.1em;
}

.message h5 {
    font-size: 1em;
}

.message h6 {
    font-size: 0.9em;
    color: #666;
}

.message p {
    margin: 8px 0;
    line-height: 1.5;
}

.message ul,
.message ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message li {
    margin: 4px 0;
}

.message strong {
    font-weight: 600;
}

.message em {
    font-style: italic;
}

.message code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
}

.message.user code {
    background: rgba(255, 255, 255, 0.2);
}

.message pre {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

.message.user pre {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.message pre code {
    background: none;
    padding: 0;
    border-radius: 0;
}

.message blockquote {
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    margin: 12px 0;
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.05);
    font-style: italic;
}

.message.user blockquote {
    border-left-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
}

.message table {
    border-collapse: collapse;
    margin: 12px 0;
    width: 100%;
    font-size: 0.9em;
}

.message th,
.message td {
    border: 1px solid rgba(0, 0, 0, 0.2);
    padding: 8px 12px;
    text-align: left;
}

.message th {
    background: rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

.message.user th,
.message.user td {
    border-color: rgba(255, 255, 255, 0.3);
}

.message.user th {
    background: rgba(255, 255, 255, 0.2);
}

.message a {
    color: inherit;
    text-decoration: underline;
    opacity: 0.9;
}

.message a:hover {
    opacity: 1;
}

.message hr {
    border: none;
    border-top: 1px solid rgba(0, 0, 0, 0.2);
    margin: 16px 0;
}

.message.user hr {
    border-top-color: rgba(255, 255, 255, 0.3);
}

/* Input Area */
.input-area {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 0 0 20px 20px;
}

.input-container {
    max-width: 900px;
    margin: 0 auto;
}

.input-row {
    display: flex;
    gap: 1.5rem;
    align-items: flex-end;
}

.input-buttons {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    resize: none;
    min-height: 50px;
    max-height: 150px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: var(--text-100);
    font-size: 1rem;
    transition: all 0.3s ease;
}

#messageInput:focus {
    outline: none;
    border-color: var(--primary-200);
    box-shadow: 0 0 0 3px rgba(46, 139, 87, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

#messageInput::placeholder {
    color: var(--text-200);
}



.attach-btn {
    padding: 1rem;
    background: linear-gradient(135deg, var(--accent-100), var(--accent-200));
    color: var(--text-100);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    cursor: pointer;
    font-weight: 500;
    height: 50px;
    min-width: 50px;
    font-size: 1.2rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(143, 188, 143, 0.3);
}

.attach-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(143, 188, 143, 0.4);
    background: linear-gradient(135deg, var(--accent-200), var(--accent-100));
}

.attach-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.image-preview {
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

.image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 4px;
}

.image-preview .remove-image {
    margin-left: 1rem;
    padding: 0.25rem 0.5rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.send-btn {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--text-100);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    cursor: pointer;
    font-weight: 600;
    height: 50px;
    font-size: 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(46, 139, 87, 0.3);
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 139, 87, 0.4);
    background: linear-gradient(135deg, var(--primary-200), var(--primary-100));
}

.send-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.5;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

/* MCP Section Organization */
.api-key-section,
.enabled-servers-section,
.add-server-section,
.registry-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
}

.api-key-section {
    border-left: 4px solid #10b981;
}

.enabled-servers-section {
    border-left: 4px solid #3b82f6;
}

.add-server-section {
    border-left: 4px solid #f59e0b;
}

.registry-section {
    border-left: 4px solid #8b5cf6;
}

.api-key-section h3,
.enabled-servers-section h3,
.add-server-section h3,
.registry-section h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Empty state for enabled servers */
.mcp-servers:empty::before {
    content: "No MCP servers enabled yet. Add servers using the sections below.";
    display: block;
    padding: 1rem;
    text-align: center;
    color: #6b7280;
    font-style: italic;
    background: #f3f4f6;
    border-radius: 6px;
    border: 1px dashed #d1d5db;
}

/* Section spacing improvements */
.api-key-section:last-child,
.enabled-servers-section:last-child,
.add-server-section:last-child,
.registry-section:last-child {
    margin-bottom: 0;
}

/* Add Server Section */
.add-server-section,
.api-key-section,
.registry-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.add-server-section h3,
.api-key-section h3,
.registry-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: #333;
}

.add-server-form,
.api-key-form {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.server-input {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    font-family: inherit;
}

.server-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.add-server-btn {
    padding: 0.75rem 1rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.add-server-btn:hover {
    background: #0056b3;
}

.add-server-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.server-help,
.api-key-help {
    margin-top: 1rem;
    padding: 1rem;
    background: #e3f2fd;
    border-radius: 4px;
    border-left: 4px solid #2196f3;
}

.server-help p,
.api-key-help p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #333;
    font-weight: 500;
}

.server-help ol,
.api-key-help ol {
    margin: 0.5rem 0 0 1rem;
    font-size: 0.85rem;
    color: #555;
}

.server-help a,
.api-key-help a {
    color: #007bff;
    text-decoration: none;
}

.server-help a:hover,
.api-key-help a:hover {
    text-decoration: underline;
}

/* Registry Section */
.registry-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.registry-results {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

.registry-loading {
    padding: 2rem;
    text-align: center;
    color: #666;
}

.registry-server {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.registry-server:hover {
    background-color: #f8f9fa;
}

.registry-server:last-child {
    border-bottom: none;
}

.registry-server-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.registry-server-name {
    font-weight: 600;
    color: #333;
    margin: 0;
}

.registry-server-author {
    font-size: 0.9rem;
    color: #666;
    margin-left: 0.5rem;
}

.registry-server-description {
    color: #555;
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.registry-server-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #777;
    margin: 0.5rem 0;
}

.registry-server-tools {
    font-size: 0.8rem;
    color: #666;
    margin: 0.5rem 0;
}

.registry-server-actions {
    margin-top: 0.5rem;
}

.add-registry-server-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.add-registry-server-btn:hover {
    background: #218838;
}

.registry-empty {
    padding: 2rem;
    text-align: center;
    color: #666;
}

/* MCP Servers */
.mcp-servers {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mcp-server {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
}

.mcp-server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.mcp-server-name {
    font-weight: 600;
    color: #333;
}

.mcp-server-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.mcp-server-status.connected {
    background: #d4edda;
    color: #155724;
}

.mcp-server-status.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.mcp-server-description {
    color: #666;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.mcp-server-tools {
    font-size: 0.875rem;
    color: #666;
}

.mcp-server-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

.connect-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.connect-btn.connect {
    background: #28a745;
    color: white;
}

.connect-btn.disconnect {
    background: #dc3545;
    color: white;
}

.connect-btn:hover {
    opacity: 0.9;
}

.connect-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.delete-btn {
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.85rem;
}

.delete-btn:hover {
    background: #c82333;
}

.delete-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Chat History */
.history-controls {
    margin-bottom: 1rem;
    text-align: right;
}

.clear-history-btn {
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.clear-history-btn:hover {
    background: #c82333;
}

.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0.5rem;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item.user {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
}

.history-item.assistant {
    background: #fff;
    border-left: 4px solid #28a745;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-role {
    font-weight: 600;
    text-transform: capitalize;
}

.history-timestamp {
    font-size: 0.8rem;
    color: #666;
}

.history-content {
    white-space: pre-wrap;
    line-height: 1.4;
}

.history-meta {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Loading */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .message {
        max-width: 95%;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}