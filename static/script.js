class OllamaChat {
    constructor() {
        this.currentModel = null;
        this.mcpServers = {};
        this.selectedImage = null;
        this.currentSessionId = null;
        this.chatSessions = {};

        this.initElements();
        this.setupEventListeners();
        this.loadModels();
        this.loadMCPServers();
        this.loadChatSessions();
    }

    initElements() {
        this.modelSelect = document.getElementById('modelSelect');
        this.mcpBtn = document.getElementById('mcpBtn');
        this.messages = document.getElementById('messages');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.mcpModal = document.getElementById('mcpModal');
        this.closeMcpModal = document.getElementById('closeMcpModal');
        this.mcpServersEl = document.getElementById('mcpServers');

        // Add server elements
        this.serverUrl = document.getElementById('serverUrl');
        this.serverName = document.getElementById('serverName');
        this.addServerBtn = document.getElementById('addServerBtn');

        // API key elements
        this.smitheryApiKey = document.getElementById('smitheryApiKey');
        this.saveApiKeyBtn = document.getElementById('saveApiKeyBtn');

        // Registry elements
        this.browseRegistryBtn = document.getElementById('browseRegistryBtn');
        this.registrySearch = document.getElementById('registrySearch');
        this.registryResults = document.getElementById('registryResults');

        // Sidebar elements
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.sidebar = document.getElementById('sidebar');
        this.newChatBtn = document.getElementById('newChatBtn');
        this.chatSessionsEl = document.getElementById('chatSessions');

        // Image elements
        this.attachBtn = document.getElementById('attachBtn');
        this.imageInput = document.getElementById('imageInput');
        this.imagePreview = document.getElementById('imagePreview');

        // Context status elements
        this.contextStatus = document.getElementById('contextStatus');
        this.contextProgress = document.getElementById('contextProgress');
        this.contextText = document.getElementById('contextText');
        this.contextWarning = document.getElementById('contextWarning');
        this.startNewSessionBtn = document.getElementById('startNewSessionBtn');
        this.dismissWarningBtn = document.getElementById('dismissWarningBtn');
    }

    setupEventListeners() {
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.modelSelect.addEventListener('change', (e) => {
            this.currentModel = e.target.value;
        });

        this.mcpBtn.addEventListener('click', () => this.openMCPModal());
        this.closeMcpModal.addEventListener('click', () => this.closeMCPModal());

        this.mcpModal.addEventListener('click', (e) => {
            if (e.target === this.mcpModal) {
                this.closeMCPModal();
            }
        });

        // Add server functionality
        this.addServerBtn.addEventListener('click', () => this.addNewServer());

        // API key functionality
        this.saveApiKeyBtn.addEventListener('click', () => this.saveApiKey());

        // Registry functionality
        this.browseRegistryBtn.addEventListener('click', () => this.toggleRegistry());
        this.registrySearch.addEventListener('input', (e) => this.filterRegistryServers(e.target.value));

        // Sidebar functionality
        this.sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        this.newChatBtn.addEventListener('click', () => this.createNewChatSession());

        // Image functionality
        this.attachBtn.addEventListener('click', () => this.imageInput.click());
        this.imageInput.addEventListener('change', (e) => this.handleImageSelect(e));

        // Context management functionality
        this.startNewSessionBtn.addEventListener('click', () => this.createNewChatSession());
        this.dismissWarningBtn.addEventListener('click', () => this.dismissContextWarning());
    }

    async loadModels() {
        try {
            const response = await fetch('/api/models');
            const data = await response.json();

            this.modelSelect.innerHTML = '<option value="">Select Model...</option>';

            if (data.success && data.models && data.models.length > 0) {
                data.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.name;
                    this.modelSelect.appendChild(option);
                });
            } else {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'No models available - Check Ollama';
                option.disabled = true;
                this.modelSelect.appendChild(option);
            }
        } catch (error) {
            console.error('Error loading models:', error);
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'Error loading models';
            option.disabled = true;
            this.modelSelect.appendChild(option);
        }
    }

    async loadMCPServers() {
        try {
            const response = await fetch('/api/mcp/servers');
            const data = await response.json();

            if (data.success && data.servers) {
                this.mcpServers = data.servers;
                this.renderMCPServers();
            }
        } catch (error) {
            console.error('Error loading MCP servers:', error);
        }
    }

    renderMCPServers() {
        this.mcpServersEl.innerHTML = '';

        Object.entries(this.mcpServers).forEach(([name, server]) => {
            const serverEl = document.createElement('div');
            serverEl.className = 'mcp-server';

            const isConnected = server.connected;
            const statusClass = isConnected ? 'connected' : 'disconnected';
            const statusText = isConnected ? 'Connected' : 'Disconnected';
            const buttonText = isConnected ? 'Disconnect' : 'Connect';
            const buttonClass = isConnected ? 'disconnect' : 'connect';

            const deleteBtnHtml = server.custom ? `<button class="delete-btn" data-server="${name}">Delete</button>` : '';

            serverEl.innerHTML = `
                <div class="mcp-server-header">
                    <div class="mcp-server-name">${this.formatServerName(name)}</div>
                    <div class="mcp-server-status ${statusClass}">${statusText}</div>
                </div>
                <div class="mcp-server-description">${server.description}</div>
                <div class="mcp-server-tools">Tools: ${server.tools.join(', ')}</div>
                <div class="mcp-server-actions">
                    <button class="connect-btn ${buttonClass}" data-server="${name}">
                        ${buttonText}
                    </button>
                    ${deleteBtnHtml}
                </div>
            `;

            const connectBtn = serverEl.querySelector('.connect-btn');
            connectBtn.addEventListener('click', () => this.toggleMCPConnection(name));

            const deleteBtn = serverEl.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => this.deleteServer(name));
            }

            this.mcpServersEl.appendChild(serverEl);
        });
    }

    formatServerName(name) {
        return name.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    async toggleMCPConnection(serverName) {
        const server = this.mcpServers[serverName];
        if (!server) return;

        const connectBtn = document.querySelector(`[data-server="${serverName}"]`);
        connectBtn.disabled = true;
        connectBtn.innerHTML = '<div class="loading"></div>';

        try {
            if (server.connected) {
                // Disconnect from server
                const response = await fetch(`/api/mcp/disconnect/${serverName}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    this.loadMCPServers();
                }
            } else {
                // Connect to server
                const response = await fetch(`/api/mcp/connect/${serverName}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    this.loadMCPServers();
                }
            }
        } catch (error) {
            console.error('Error toggling MCP connection:', error);
        } finally {
            connectBtn.disabled = false;
        }
    }

    async addNewServer() {
        const url = this.serverUrl.value.trim();
        const name = this.serverName.value.trim();

        if (!url) {
            alert('Please enter a server URL');
            return;
        }

        // Generate server ID from URL
        const serverId = this.generateServerId(url);
        const serverName = name || this.extractServerName(url);

        this.addServerBtn.disabled = true;
        this.addServerBtn.textContent = 'Adding...';

        try {
            const response = await fetch('/api/mcp/add-server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: serverId,
                    name: serverName,
                    url: url,
                    description: `Custom MCP server: ${serverName}`
                })
            });

            const result = await response.json();

            if (result.success) {
                this.serverUrl.value = '';
                this.serverName.value = '';
                this.loadMCPServers();
                alert(`Server "${serverName}" added successfully!`);
            } else {
                alert(`Failed to add server: ${result.error}`);
            }
        } catch (error) {
            alert(`Error adding server: ${error.message}`);
        } finally {
            this.addServerBtn.disabled = false;
            this.addServerBtn.textContent = 'Add Server';
        }
    }

    generateServerId(url) {
        // Extract a clean ID from the URL
        const match = url.match(/server\.smithery\.ai\/(@[^\/]+\/[^\/]+)/);
        if (match) {
            return match[1].replace(/[@\/]/g, '_').toLowerCase();
        }
        return 'custom_' + Date.now();
    }

    extractServerName(url) {
        // Extract server name from Smithery URL
        const match = url.match(/server\.smithery\.ai\/@[^\/]+\/([^\/]+)/);
        if (match) {
            return match[1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
        return 'Custom Server';
    }

    async deleteServer(serverName) {
        if (!confirm(`Are you sure you want to delete the server "${this.formatServerName(serverName)}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/mcp/delete-server/${serverName}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.loadMCPServers();
                alert(`Server "${this.formatServerName(serverName)}" deleted successfully!`);
            } else {
                alert(`Failed to delete server: ${result.error}`);
            }
        } catch (error) {
            alert(`Error deleting server: ${error.message}`);
        }
    }

    openMCPModal() {
        this.mcpModal.style.display = 'flex';
        this.loadMCPServers();
        this.loadApiKey();
    }

    closeMCPModal() {
        this.mcpModal.style.display = 'none';
    }

    async saveApiKey() {
        const apiKey = this.smitheryApiKey.value.trim();
        if (!apiKey) {
            alert('Please enter a valid API key');
            return;
        }

        try {
            // Send API key to backend
            const response = await fetch('/api/mcp/set-api-key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ api_key: apiKey })
            });

            const result = await response.json();

            if (result.success) {
                // Store API key in localStorage
                localStorage.setItem('smithery_api_key', apiKey);

                // Show success message
                this.saveApiKeyBtn.textContent = 'Saved!';
                this.saveApiKeyBtn.style.background = '#28a745';

                setTimeout(() => {
                    this.saveApiKeyBtn.textContent = 'Save API Key';
                    this.saveApiKeyBtn.style.background = '';
                }, 2000);
            } else {
                alert(`Error saving API key: ${result.error}`);
            }
        } catch (error) {
            alert(`Error saving API key: ${error.message}`);
        }
    }

    async loadApiKey() {
        const savedApiKey = localStorage.getItem('smithery_api_key');
        if (savedApiKey) {
            this.smitheryApiKey.value = savedApiKey;

            // Send API key to backend
            try {
                await fetch('/api/mcp/set-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ api_key: savedApiKey })
                });
            } catch (error) {
                console.error('Error setting API key on backend:', error);
            }
        }
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) {
            alert('Please enter a message');
            return;
        }
        if (!this.currentModel) {
            alert('Please select a model first');
            return;
        }

        const hasImage = !!this.selectedImage;
        this.addMessage(message, 'user', true, hasImage);
        this.messageInput.value = '';
        this.sendBtn.disabled = true;

        const requestBody = {
            message: message,
            model: this.currentModel,
            stream: true,
            session_id: this.currentSessionId
        };

        if (this.selectedImage) {
            requestBody.images = [this.selectedImage.split(',')[1]]; // Remove data:image/...;base64, prefix
        }

        // Create a placeholder message for the assistant response
        const assistantMessageEl = this.createStreamingMessage();

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Handle streaming response
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let fullResponse = '';

            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop(); // Keep incomplete line in buffer

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));

                            if (data.error) {
                                this.updateStreamingMessage(assistantMessageEl, `Error: ${data.message}`, true);
                                break;
                            }

                            if (data.response) {
                                fullResponse += data.response;
                                this.updateStreamingMessage(assistantMessageEl, fullResponse);
                            }

                            // Skip tool execution display - tools are now hidden and processed by LLM
                            // The final response will contain the LLM-processed summary

                            if (data.done) {
                                // Check if we have HTML content and context status
                                const contextStatus = data.context_status;
                                const responseHtml = data.response_html;
                                this.finalizeStreamingMessage(assistantMessageEl, fullResponse, responseHtml, contextStatus);
                                break;
                            }
                        } catch (e) {
                            console.error('Error parsing streaming data:', e);
                        }
                    }
                }
            }

        } catch (error) {
            // Fallback to non-streaming if streaming fails
            try {
                const fallbackResponse = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ...requestBody,
                        stream: false
                    })
                });

                if (fallbackResponse.ok) {
                    const data = await fallbackResponse.json();
                    if (data.success) {
                        // Use HTML content if available, hide tool execution details
                        const content = data.response;
                        const contentHtml = data.response_html;
                        const contextStatus = data.context_status;

                        this.finalizeStreamingMessage(assistantMessageEl, content, contentHtml, contextStatus);
                    } else {
                        this.updateStreamingMessage(assistantMessageEl, `Error: ${data.error || 'Unknown error'}`, true);
                    }
                } else {
                    this.updateStreamingMessage(assistantMessageEl, `Error: ${error.message}`, true);
                }
            } catch (fallbackError) {
                this.updateStreamingMessage(assistantMessageEl, `Error: ${error.message}`, true);
            }
        } finally {
            this.sendBtn.disabled = false;
            this.removeImage(); // Clear image after sending
        }
    }

    createStreamingMessage() {
        const messageEl = document.createElement('div');
        messageEl.className = 'message assistant streaming';
        messageEl.innerHTML = '<span class="typing-indicator">●●●</span>';
        this.messages.appendChild(messageEl);
        this.messages.scrollTop = this.messages.scrollHeight;
        return messageEl;
    }

    updateStreamingMessage(messageEl, content, isError = false) {
        if (isError) {
            messageEl.className = 'message error';
            messageEl.textContent = content;
        } else {
            messageEl.className = 'message assistant';
            // For streaming, just update the text content directly
            messageEl.textContent = content;
        }
        this.messages.scrollTop = this.messages.scrollHeight;
    }

    finalizeStreamingMessage(messageEl, content, contentHtml = null, contextStatus = null) {
        messageEl.className = 'message assistant';

        // Clear existing content and rebuild with proper structure
        messageEl.innerHTML = '';

        // Create message content wrapper
        const contentWrapper = document.createElement('div');
        contentWrapper.className = 'message-content';

        // Use HTML content if available (for markdown), otherwise use text
        if (contentHtml) {
            contentWrapper.innerHTML = contentHtml;
        } else {
            contentWrapper.textContent = content;
        }

        messageEl.appendChild(contentWrapper);

        // Add message actions for assistant messages
        this.addMessageActions(messageEl, 'assistant', content);

        this.messages.scrollTop = this.messages.scrollHeight;

        // Update context status if provided
        if (contextStatus) {
            this.updateContextStatus(contextStatus);
        }
    }



    async loadChatSessions() {
        try {
            const response = await fetch('/api/chat/sessions');
            const data = await response.json();

            if (data.success) {
                this.chatSessions = data.sessions;
                this.currentSessionId = data.current_session;
                this.renderChatSessions();

                // Load current session messages if exists
                if (this.currentSessionId) {
                    await this.loadSessionMessages(this.currentSessionId);
                }
            }
        } catch (error) {
            console.error('Error loading chat sessions:', error);
        }
    }

    renderChatSessions() {
        this.chatSessionsEl.innerHTML = '';

        if (this.chatSessions.length === 0) {
            this.chatSessionsEl.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">No chat sessions yet.</p>';
            return;
        }

        this.chatSessions.forEach(session => {
            const sessionEl = document.createElement('div');
            sessionEl.className = `chat-session ${session.id === this.currentSessionId ? 'active' : ''}`;
            sessionEl.dataset.sessionId = session.id;

            sessionEl.innerHTML = `
                <div class="chat-session-info">
                    <div class="chat-session-title">${session.title}</div>
                    <div class="chat-session-preview">${session.last_message}</div>
                </div>
                <div class="chat-session-actions">
                    <button class="delete-session-btn" data-session-id="${session.id}">×</button>
                </div>
            `;

            // Add click handler for session switching
            sessionEl.addEventListener('click', (e) => {
                if (!e.target.classList.contains('delete-session-btn')) {
                    this.switchToSession(session.id);
                }
            });

            // Add delete handler
            const deleteBtn = sessionEl.querySelector('.delete-session-btn');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteSession(session.id);
            });

            this.chatSessionsEl.appendChild(sessionEl);
        });
    }

    async createNewChatSession() {
        try {
            const response = await fetch('/api/chat/sessions', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                this.currentSessionId = data.session_id;
                this.messages.innerHTML = '';
                await this.loadChatSessions();
            }
        } catch (error) {
            console.error('Error creating new chat session:', error);
        }
    }

    async switchToSession(sessionId) {
        try {
            const response = await fetch(`/api/chat/sessions/${sessionId}/switch`, {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                this.currentSessionId = sessionId;
                this.messages.innerHTML = '';

                // Load session messages
                data.messages.forEach(msg => {
                    this.addMessage(
                        msg.content,
                        msg.role,
                        false,
                        msg.has_image,
                        msg.content_html,
                        msg.context_status
                    );
                });

                await this.loadChatSessions();
            }
        } catch (error) {
            console.error('Error switching to session:', error);
        }
    }

    async deleteSession(sessionId) {
        if (!confirm('Are you sure you want to delete this chat session?')) {
            return;
        }

        try {
            const response = await fetch(`/api/chat/sessions/${sessionId}`, {
                method: 'DELETE'
            });
            const data = await response.json();

            if (data.success) {
                this.currentSessionId = data.current_session;

                // Clear messages if deleted session was current
                if (sessionId === this.currentSessionId) {
                    this.messages.innerHTML = '';
                }

                await this.loadChatSessions();
            }
        } catch (error) {
            console.error('Error deleting session:', error);
        }
    }

    async loadSessionMessages(sessionId) {
        try {
            const response = await fetch(`/api/chat/sessions/${sessionId}`);
            const data = await response.json();

            if (data.success) {
                this.messages.innerHTML = '';
                data.session.messages.forEach(msg => {
                    this.addMessage(
                        msg.content,
                        msg.role,
                        false,
                        msg.has_image,
                        msg.content_html,
                        msg.context_status
                    );
                });
            }
        } catch (error) {
            console.error('Error loading session messages:', error);
        }
    }

    toggleSidebar() {
        this.sidebar.classList.toggle('hidden');
        // Also toggle the main content class for dynamic positioning
        const mainContent = document.querySelector('.main-content');
        mainContent.classList.toggle('sidebar-hidden');
    }

    handleImageSelect(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.selectedImage = e.target.result;
                this.showImagePreview(file.name, e.target.result);
            };
            reader.readAsDataURL(file);
        }
    }

    showImagePreview(fileName, dataUrl) {
        this.imagePreview.innerHTML = `
            <div style="display: flex; align-items: center; gap: 1rem;">
                <img src="${dataUrl}" alt="${fileName}" style="max-width: 100px; max-height: 100px; border-radius: 4px;">
                <div>
                    <div style="font-weight: 500;">${fileName}</div>
                    <button class="remove-image" onclick="ollamaChat.removeImage()">Remove</button>
                </div>
            </div>
        `;
        this.imagePreview.style.display = 'block';
    }

    removeImage() {
        this.selectedImage = null;
        this.imagePreview.style.display = 'none';
        this.imageInput.value = '';
    }

    addMessage(content, type, saveToHistory = false, hasImage = false, contentHtml = null, contextStatus = null) {
        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;

        // Create message content wrapper
        const contentWrapper = document.createElement('div');
        contentWrapper.className = 'message-content';

        // Use HTML content if available (for markdown), otherwise use text
        if (contentHtml && type === 'assistant') {
            contentWrapper.innerHTML = contentHtml;
        } else {
            contentWrapper.textContent = content;
        }

        messageEl.appendChild(contentWrapper);

        // Add message actions
        this.addMessageActions(messageEl, type, content, hasImage);

        // Add edit form for user messages (hidden by default)
        if (type === 'user') {
            this.addEditForm(messageEl, content);
        }

        if (hasImage && type === 'user') {
            const imageIcon = document.createElement('span');
            imageIcon.textContent = ' 📷';
            imageIcon.style.opacity = '0.7';
            contentWrapper.appendChild(imageIcon);
        }

        this.messages.appendChild(messageEl);
        this.messages.scrollTop = this.messages.scrollHeight;

        // Update context status if provided
        if (contextStatus) {
            this.updateContextStatus(contextStatus);
        }

        return messageEl;
    }

    updateContextStatus(contextStatus) {
        if (!contextStatus) return;

        const { current_tokens, context_window, usage_percentage, should_start_new_session } = contextStatus;

        // Show context status
        this.contextStatus.style.display = 'flex';
        this.contextProgress.style.width = `${Math.min(usage_percentage, 100)}%`;
        this.contextText.textContent = `${Math.round(usage_percentage)}% used`;

        // Show warning if context is nearly full
        if (should_start_new_session) {
            this.contextWarning.style.display = 'block';
        }
    }

    dismissContextWarning() {
        this.contextWarning.style.display = 'none';
    }

    addMessageActions(messageEl, type, content, hasImage = false) {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'message-actions';

        if (type === 'user') {
            // Edit button
            const editBtn = document.createElement('button');
            editBtn.className = 'message-action-btn';
            editBtn.innerHTML = '✏️';
            editBtn.title = 'Edit message';
            editBtn.onclick = () => this.editMessage(messageEl);
            actionsDiv.appendChild(editBtn);

            // Resend button
            const resendBtn = document.createElement('button');
            resendBtn.className = 'message-action-btn';
            resendBtn.innerHTML = '🔄';
            resendBtn.title = 'Resend message';
            resendBtn.onclick = () => this.resendMessage(content, hasImage);
            actionsDiv.appendChild(resendBtn);
        } else if (type === 'assistant') {
            // Copy button
            const copyBtn = document.createElement('button');
            copyBtn.className = 'message-action-btn';
            copyBtn.innerHTML = '📋';
            copyBtn.title = 'Copy response';
            copyBtn.onclick = () => this.copyMessage(content);
            actionsDiv.appendChild(copyBtn);

            // Regenerate button
            const regenBtn = document.createElement('button');
            regenBtn.className = 'message-action-btn';
            regenBtn.innerHTML = '🔄';
            regenBtn.title = 'Regenerate response';
            regenBtn.onclick = () => this.regenerateResponse(messageEl);
            actionsDiv.appendChild(regenBtn);
        }

        messageEl.appendChild(actionsDiv);
    }

    addEditForm(messageEl, content) {
        const editForm = document.createElement('div');
        editForm.className = 'message-edit-form';

        const textarea = document.createElement('textarea');
        textarea.className = 'message-edit-textarea';
        textarea.value = content;
        editForm.appendChild(textarea);

        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'message-edit-actions';

        const saveBtn = document.createElement('button');
        saveBtn.className = 'message-edit-btn save';
        saveBtn.textContent = 'Save & Send';
        saveBtn.onclick = () => this.saveEditedMessage(messageEl, textarea.value);
        actionsDiv.appendChild(saveBtn);

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'message-edit-btn cancel';
        cancelBtn.textContent = 'Cancel';
        cancelBtn.onclick = () => this.cancelEdit(messageEl);
        actionsDiv.appendChild(cancelBtn);

        editForm.appendChild(actionsDiv);
        messageEl.appendChild(editForm);
    }

    editMessage(messageEl) {
        messageEl.classList.add('editing');
        const textarea = messageEl.querySelector('.message-edit-textarea');
        textarea.focus();
        textarea.select();
    }

    cancelEdit(messageEl) {
        messageEl.classList.remove('editing');
    }

    async saveEditedMessage(messageEl, newContent) {
        if (!newContent.trim()) {
            alert('Message cannot be empty');
            return;
        }

        // Update the message content
        const contentWrapper = messageEl.querySelector('.message-content');
        contentWrapper.textContent = newContent;

        // Exit edit mode
        messageEl.classList.remove('editing');

        // Resend the edited message
        await this.resendMessage(newContent, false);
    }

    async resendMessage(content, hasImage = false) {
        if (!content.trim()) {
            alert('Message cannot be empty');
            return;
        }

        // Clear any existing assistant messages after the user message
        this.clearMessagesAfterLast('user');

        // Send the message
        this.sendBtn.disabled = true;
        this.messageInput.value = content;

        const requestBody = {
            message: content,
            model: this.modelSelect.value,
            stream: this.streamToggle.checked,
            session_id: this.currentSessionId
        };

        if (hasImage && this.selectedImage) {
            requestBody.image = this.selectedImage;
        }

        try {
            if (this.streamToggle.checked) {
                await this.handleStreamingResponse(requestBody);
            } else {
                await this.handleNonStreamingResponse(requestBody);
            }
        } catch (error) {
            console.error('Error resending message:', error);
            this.addMessage('Error resending message. Please try again.', 'error');
        } finally {
            this.sendBtn.disabled = false;
            this.messageInput.value = '';
        }
    }

    async copyMessage(content) {
        try {
            await navigator.clipboard.writeText(content);

            // Show temporary feedback
            const notification = document.createElement('div');
            notification.textContent = 'Copied to clipboard!';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                z-index: 1000;
                font-size: 14px;
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 2000);
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            alert('Failed to copy to clipboard');
        }
    }

    async regenerateResponse(messageEl) {
        // Find the last user message
        const userMessages = Array.from(this.messages.querySelectorAll('.message.user'));
        const lastUserMessage = userMessages[userMessages.length - 1];

        if (!lastUserMessage) {
            alert('No user message found to regenerate response for');
            return;
        }

        const userContent = lastUserMessage.querySelector('.message-content').textContent;

        // Remove the current assistant message and any messages after it
        let currentMessage = messageEl;
        while (currentMessage) {
            const nextMessage = currentMessage.nextElementSibling;
            currentMessage.remove();
            currentMessage = nextMessage;
        }

        // Resend the last user message
        await this.resendMessage(userContent, false);
    }

    clearMessagesAfterLast(messageType) {
        const messages = Array.from(this.messages.querySelectorAll('.message'));
        const lastMessageIndex = messages.findLastIndex(msg => msg.classList.contains(messageType));

        if (lastMessageIndex !== -1) {
            // Remove all messages after the last message of the specified type
            for (let i = messages.length - 1; i > lastMessageIndex; i--) {
                messages[i].remove();
            }
        }
    }

    async toggleRegistry() {
        if (this.registryResults.style.display === 'none') {
            this.registryResults.style.display = 'block';
            this.registrySearch.style.display = 'block';
            this.browseRegistryBtn.textContent = 'Hide Registry';
            await this.loadSmitheryRegistry();
        } else {
            this.registryResults.style.display = 'none';
            this.registrySearch.style.display = 'none';
            this.browseRegistryBtn.textContent = 'Browse Available Servers';
        }
    }

    async loadSmitheryRegistry() {
        this.registryResults.innerHTML = '<div class="registry-loading">Loading servers...</div>';

        try {
            const response = await fetch('/api/mcp/registry');
            const result = await response.json();

            if (result.success) {
                this.displayRegistryServers(result.servers);
            } else {
                this.registryResults.innerHTML = `<div class="registry-empty">Error loading registry: ${result.error}</div>`;
            }
        } catch (error) {
            console.error('Error loading registry:', error);
            this.registryResults.innerHTML = '<div class="registry-empty">Failed to load registry</div>';
        }
    }

    displayRegistryServers(servers) {
        if (!servers || servers.length === 0) {
            this.registryResults.innerHTML = '<div class="registry-empty">No servers found in registry</div>';
            return;
        }

        const serversHtml = servers.map(server => `
            <div class="registry-server" data-server-id="${server.id}">
                <div class="registry-server-header">
                    <h4 class="registry-server-name">${server.name}</h4>
                    <span class="registry-server-author">by ${server.author}</span>
                </div>
                <p class="registry-server-description">${server.description}</p>
                <div class="registry-server-meta">
                    <span>Version: ${server.version}</span>
                    <span>Tools: ${server.tools_count}</span>
                </div>
                <div class="registry-server-tools">
                    <strong>Available tools:</strong> ${server.tools.join(', ')}
                </div>
                <div class="registry-server-actions">
                    <button class="add-registry-server-btn" onclick="ollamaChat.addRegistryServer('${server.id}', '${server.name}', '${server.url}', '${server.description}')">
                        Add Server
                    </button>
                </div>
            </div>
        `).join('');

        this.registryResults.innerHTML = serversHtml;
    }

    filterRegistryServers(searchTerm) {
        const servers = this.registryResults.querySelectorAll('.registry-server');
        const term = searchTerm.toLowerCase();

        servers.forEach(server => {
            const name = server.querySelector('.registry-server-name').textContent.toLowerCase();
            const description = server.querySelector('.registry-server-description').textContent.toLowerCase();
            const author = server.querySelector('.registry-server-author').textContent.toLowerCase();
            const tools = server.querySelector('.registry-server-tools').textContent.toLowerCase();

            if (name.includes(term) || description.includes(term) || author.includes(term) || tools.includes(term)) {
                server.style.display = 'block';
            } else {
                server.style.display = 'none';
            }
        });
    }

    async addRegistryServer(id, name, url, description) {
        try {
            const response = await fetch('/api/mcp/add-server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: id,
                    name: name,
                    url: url,
                    description: description
                })
            });

            const result = await response.json();
            if (result.success) {
                alert(`Server "${name}" added successfully!`);
                await this.loadMCPServers(); // Refresh the server list
            } else {
                alert(`Error adding server: ${result.error}`);
            }
        } catch (error) {
            console.error('Error adding registry server:', error);
            alert('Failed to add server');
        }
    }


}

// Global instance for onclick handlers
let ollamaChat;

document.addEventListener('DOMContentLoaded', () => {
    ollamaChat = new OllamaChat();
});
