# Ollama Enhanced Chat Application

A professional, enterprise-grade web-based chat interface for Ollama with advanced features including image support, web search, and MCP server integration.

## 🚀 Features

### Core Features
- **Real-time Streaming Chat**: Live AI responses with typing indicators
- **Multi-Model Support**: Switch between different Ollama models
- **Professional UI**: Enterprise-grade Material Design interface
- **Responsive Design**: Works on desktop, tablet, and mobile

### Advanced Features
- **📷 Image Support**: Upload and analyze images with vision models
- **🔍 Web Search**: Integrated DuckDuckGo search via MCP servers
- **🔌 MCP Integration**: Model Context Protocol server support
- **📁 Drag & Drop**: Easy image attachment via drag and drop
- **📊 Session Analytics**: Message count and session time tracking
- **💾 Export Chat**: Export conversations to JSON format
- **⚙️ Advanced Settings**: Temperature, max tokens, and other parameters

### MCP Server Integration
- **DuckDuckGo Search**: Web search capabilities
- **Weather Information**: Real-time weather data
- **File System Operations**: File management tools
- **Extensible Architecture**: Easy to add more MCP servers

## 🏗️ Architecture

\`\`\`
Frontend (HTML/JS) ↔ Flask Backend ↔ Ollama + MCP Servers
\`\`\`

## 📁 Project Structure

\`\`\`
ollama-chat/
├── app.py                 # Enhanced Flask backend
├── requirements.txt       # Python dependencies
├── README.md             # Documentation
├── templates/
│   └── index.html        # Professional HTML interface
├── static/
│   ├── style.css         # Professional CSS styles
│   └── script.js         # Enhanced JavaScript
└── uploads/              # Temporary image uploads
\`\`\`

## 🛠️ Setup Instructions

### 1. Install Dependencies

\`\`\`bash
pip install -r requirements.txt
\`\`\`

### 2. Start Ollama

\`\`\`bash
ollama serve
\`\`\`

### 3. Install Vision Models (Optional)

For image analysis capabilities:

\`\`\`bash
ollama pull llava
ollama pull bakllava
\`\`\`

### 4. Run the Application

\`\`\`bash
python app.py
\`\`\`

### 5. Open Your Browser

Navigate to: `http://localhost:5000`

## 🔧 Configuration

### MCP Servers

The application comes pre-configured with Smithery.ai MCP servers:

- **DuckDuckGo Search**: Web search functionality
- **Weather Service**: Weather information
- **File System**: File operations

### Supported Image Formats

- PNG, JPG, JPEG, GIF, WebP, BMP
- Maximum file size: 16MB
- Automatic resizing to 1024x1024 for optimal performance

## 🎯 Usage

### Basic Chat
1. Select a model from the sidebar
2. Type your message and press Enter or ⌘+Enter
3. View real-time AI responses

### Image Analysis
1. Click the attachment button or drag & drop images
2. Images are automatically processed and sent with your message
3. Works with vision-capable models (llava, bakllava, etc.)

### Web Search
1. Use natural language like "search for latest AI news"
2. The system automatically detects search intent
3. Results are included in the AI's context

### Advanced Settings
- Adjust temperature for creativity control
- Set max tokens for response length
- Export chat history as JSON
- Clear chat history
- Toggle fullscreen mode

## 🔍 API Endpoints

- `GET /` - Main chat interface
- `GET /api/status` - System status and MCP server info
- `GET /api/models` - Available Ollama models
- `POST /api/chat` - Send chat messages with image/search support
- `POST /api/upload` - Upload and process images
- `POST /api/search` - Direct web search
- `GET /api/mcp/servers` - Available MCP servers
- `POST /api/mcp/connect/<server>` - Connect to MCP server

## 🚨 Troubleshooting

### Common Issues

1. **Cannot connect to Ollama**
   - Ensure Ollama is running: `ollama serve`
   - Check if port 11434 is accessible

2. **Models not loading**
   - Verify Ollama has models: `ollama list`
   - Try refreshing models in the interface

3. **Image upload fails**
   - Check file size (max 16MB)
   - Ensure file format is supported
   - Verify disk space for uploads

4. **Search not working**
   - MCP servers require internet connection
   - Check Smithery.ai API status

### Performance Tips

- Use smaller images for faster processing
- Close unused browser tabs for better performance
- Clear chat history periodically for optimal speed

## 🔐 Security Notes

- Images are processed locally and not stored permanently
- MCP connections use secure HTTPS
- No chat data is stored on external servers
- All processing happens locally or through secure APIs

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review the API documentation
- Submit issues on GitHub
